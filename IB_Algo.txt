// This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
//@version=5

indicator("IB Algo", shorttitle="IB Algo", overlay=true)

daySess = input.session("0930-1600","Session Period",options=["0930-1600","0700-1030","0720-1030","0915-1530"])
orSess = input.session("0930-1030", "Open Range", options=["0930-0945", "0930-1000", "0930-1030","0700-0800","0720-0930","1330-1600","0200-0500","1200-1330","0915-0945"])
show_extension = input.bool(true,"Show Extension","Display extended bars on the second day")

//Bars
is_newbar(sess) =>
    t = time("D", sess, "America/New_York")
    na(t[1]) and not na(t) or t[1] < t

is_session(sess) =>
    not na(time(timeframe.period, sess, "America/New_York"))

nyNewbar = is_newbar(daySess)

bool inOpenRange = is_session(orSess)
int opening_bar = na
opening_bar := opening_bar[1]

float or_high = na
float or_low = na

float last_or_high = na
float last_or_low = na


if inOpenRange and not inOpenRange[1]
    last_or_high := na
    last_or_low := na
    or_high := na
    or_low := na
    opening_bar := bar_index

else if inOpenRange and inOpenRange[1] and not inOpenRange[2]
    last_or_high := or_high[3]
    last_or_low := or_low[3]
    or_high := math.max(high,high[1])
    or_low := math.min(low,low[1])

else if inOpenRange
    or_high := math.max(or_high[1],high)
    or_low := math.min(or_low[1],low)
    last_or_high := last_or_high[1]
    last_or_low := last_or_low[1]

else
    or_high := or_high[1]
    or_low := or_low[1]
    last_or_high := last_or_high[1]
    last_or_low := last_or_low[1]


or_high_line = plot(or_high,"Open Range High",color.gray,2,style=plot.style_linebr)
or_low_line = plot(or_low,"Open Range LOW",color.gray,2,style=plot.style_linebr)

fill(or_high_line,or_low_line,color.new(color.gray,95))

// Calculate and plot developing midpoint
float or_mid = not na(or_high) and not na(or_low) ? (or_high + or_low)/2 : na
or_mid_line = plot(or_mid,"Open Range Midpoint",color.white,1,style=plot.style_linebr)

last_or_high_line = plot(show_extension?last_or_high:na,"Last Open Range High",color.gray,1,style=plot.style_linebr)
last_or_low_line = plot(show_extension?last_or_low:na,"Last Open Range LOW",color.gray,1,style=plot.style_linebr,display=display.price_scale)

// Variables to store labels
var label or_high_label = na
var label or_low_label = na
var label or_mid_label = na
var label last_or_high_label = na
var label last_or_low_label = na

// Add labels for Open Range lines
if barstate.islast
    // Delete previous labels to prevent snail trail
    if not na(or_high_label)
        label.delete(or_high_label)
    if not na(or_low_label)
        label.delete(or_low_label)
    if not na(or_mid_label)
        label.delete(or_mid_label)
    if not na(last_or_high_label)
        label.delete(last_or_high_label)
    if not na(last_or_low_label)
        label.delete(last_or_low_label)

    // Create new labels
    if not na(or_high)
        or_high_label := label.new(bar_index, or_high, "OR High", style=label.style_none, textcolor=color.gray, size=size.huge, textalign=text.align_right, yloc=yloc.price)

    if not na(or_low)
        or_low_label := label.new(bar_index, or_low, "OR Low", style=label.style_none, textcolor=color.gray, size=size.huge, textalign=text.align_right, yloc=yloc.price)

    if not na(or_mid)
        or_mid_label := label.new(bar_index, or_mid, "OR Mid", style=label.style_none, textcolor=color.white, size=size.huge, textalign=text.align_right, yloc=yloc.price)

    if show_extension and not na(last_or_high)
        last_or_high_label := label.new(bar_index, last_or_high, "Last OR High", style=label.style_none, textcolor=color.gray, size=size.huge, textalign=text.align_right, yloc=yloc.price)

    if show_extension and not na(last_or_low)
        last_or_low_label := label.new(bar_index, last_or_low, "Last OR Low", style=label.style_none, textcolor=color.gray, size=size.huge, textalign=text.align_right, yloc=yloc.price)

// Vertical lines at 9:30, 10:30, and 11:30
var line start_line = na
var line end_line = na
var line hour_line = na

// Check for 11:30 time
bool is1130 = hour(time, "America/New_York") == 11 and minute(time, "America/New_York") == 30

if inOpenRange and not inOpenRange[1]
    // Draw vertical line at 9:30 (start of opening range)
    start_line := line.new(bar_index, low, bar_index, high, extend=extend.both, color=color.white, style=line.style_dotted, width=1)

if inOpenRange[1] and not inOpenRange
    // Draw vertical line at 10:30 (end of opening range)
    end_line := line.new(bar_index, low, bar_index, high, extend=extend.both, color=color.white, style=line.style_dotted, width=1)

if is1130 and not is1130[1]
    // Draw vertical line at 11:30
    hour_line := line.new(bar_index, low, bar_index, high, extend=extend.both, color=color.white, style=line.style_dotted, width=1)

// Calculate IB range and extension levels
float ib_range = not na(or_high) and not na(or_low) ? or_high - or_low : na

// Calculate 25% and 50% extensions above IB high
float ib_high_25_ext = not na(or_high) and not na(ib_range) ? or_high + (ib_range * 0.25) : na
float ib_high_50_ext = not na(or_high) and not na(ib_range) ? or_high + (ib_range * 0.50) : na

// Calculate 25% and 50% extensions below IB low
float ib_low_25_ext = not na(or_low) and not na(ib_range) ? or_low - (ib_range * 0.25) : na
float ib_low_50_ext = not na(or_low) and not na(ib_range) ? or_low - (ib_range * 0.50) : na

// Plot extension lines
ib_high_25_ext_line = plot(ib_high_25_ext, "IB High 25% Extension", color.new(color.gray, 60), 1, style=plot.style_linebr)
ib_high_50_ext_line = plot(ib_high_50_ext, "IB High 50% Extension", color.new(color.gray, 40), 1, style=plot.style_linebr)
ib_low_25_ext_line = plot(ib_low_25_ext, "IB Low 25% Extension", color.new(color.gray, 60), 1, style=plot.style_linebr)
ib_low_50_ext_line = plot(ib_low_50_ext, "IB Low 50% Extension", color.new(color.gray, 40), 1, style=plot.style_linebr)

// Variables to store extension labels
var label ib_high_25_label = na
var label ib_high_50_label = na
var label ib_low_25_label = na
var label ib_low_50_label = na

// Add labels for extension lines
if barstate.islast
    // Delete previous extension labels to prevent snail trail
    if not na(ib_high_25_label)
        label.delete(ib_high_25_label)
    if not na(ib_high_50_label)
        label.delete(ib_high_50_label)
    if not na(ib_low_25_label)
        label.delete(ib_low_25_label)
    if not na(ib_low_50_label)
        label.delete(ib_low_50_label)

    // Create new extension labels
    if not na(ib_high_25_ext)
        ib_high_25_label := label.new(bar_index, ib_high_25_ext, "IB High +25%", style=label.style_none, textcolor=color.new(color.gray, 60), size=size.huge, textalign=text.align_right, yloc=yloc.price)

    if not na(ib_high_50_ext)
        ib_high_50_label := label.new(bar_index, ib_high_50_ext, "IB High +50%", style=label.style_none, textcolor=color.new(color.gray, 40), size=size.huge, textalign=text.align_right, yloc=yloc.price)

    if not na(ib_low_25_ext)
        ib_low_25_label := label.new(bar_index, ib_low_25_ext, "IB Low -25%", style=label.style_none, textcolor=color.new(color.gray, 60), size=size.huge, textalign=text.align_right, yloc=yloc.price)

    if not na(ib_low_50_ext)
        ib_low_50_label := label.new(bar_index, ib_low_50_ext, "IB Low -50%", style=label.style_none, textcolor=color.new(color.gray, 40), size=size.huge, textalign=text.align_right, yloc=yloc.price)
